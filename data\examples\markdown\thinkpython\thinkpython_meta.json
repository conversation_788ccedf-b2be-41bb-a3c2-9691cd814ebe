{"table_of_contents": [{"title": "Think Python", "heading_level": null, "page_id": 0, "polygon": [[398.935546875, 265.*********], [525.6013793945312, 265.*********], [525.6013793945312, 289.6333312988281], [398.935546875, 289.6333312988281]]}, {"title": "How to Think Like a Computer Scientist", "heading_level": null, "page_id": 0, "polygon": [[267.3017578125, 306.861328125], [525.6033325195312, 306.861328125], [525.6033325195312, 323.876953125], [267.3017578125, 323.876953125]]}, {"title": "Think Python", "heading_level": null, "page_id": 2, "polygon": [[398.63671875, 264.90234375], [525.6013793945312, 264.90234375], [525.6013793945312, 289.6333312988281], [398.63671875, 289.6333312988281]]}, {"title": "How to Think Like a Computer Scientist", "heading_level": null, "page_id": 2, "polygon": [[267.451171875, 306.66796875], [525.6033325195312, 306.66796875], [525.6033325195312, 323.7117614746094], [267.451171875, 323.7117614746094]]}, {"title": "Preface", "heading_level": null, "page_id": 4, "polygon": [[128.49609375, 165.322265625], [213.662109375, 165.322265625], [213.662109375, 190.65838623046875], [128.49609375, 190.65838623046875]]}, {"title": "The strange history of this book", "heading_level": null, "page_id": 4, "polygon": [[129.31787109375, 237.638671875], [338.8106384277344, 237.638671875], [338.8106384277344, 253.55902099609375], [129.31787109375, 253.55902099609375]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 5, "polygon": [[85.83837890625, 510.08203125], [211.5703125, 510.08203125], [211.5703125, 526.0249328613281], [85.83837890625, 526.0249328613281]]}, {"title": "Contributor List", "heading_level": null, "page_id": 6, "polygon": [[128.0478515625, 84.498046875], [235.97708129882812, 84.498046875], [235.97708129882812, 100.29998779296875], [128.0478515625, 100.29998779296875]]}, {"title": "Contents", "heading_level": null, "page_id": 12, "polygon": [[128.0478515625, 165.40826416015625], [231.47499084472656, 165.40826416015625], [231.47499084472656, 190.1953125], [128.0478515625, 190.1953125]]}, {"title": "Chapter 1", "heading_level": null, "page_id": 22, "polygon": [[128.197265625, 164.801513671875], [220.84561157226562, 164.801513671875], [220.84561157226562, 185.4639892578125], [128.197265625, 185.4639892578125]]}, {"title": "The way of the program", "heading_level": null, "page_id": 22, "polygon": [[127.97314453125, 220.*********], [400.91961669921875, 220.*********], [400.91961669921875, 245.6234130859375], [127.97314453125, 245.6234130859375]]}, {"title": "1.1 The Python programming language", "heading_level": null, "page_id": 22, "polygon": [[128.9443359375, 498.8671875], [395.05078125, 498.8671875], [395.05078125, 513.9990234375], [128.9443359375, 513.9990234375]]}, {"title": "1.2 What is a program?", "heading_level": null, "page_id": 24, "polygon": [[128.3466796875, 85.95379638671875], [288.8173828125, 85.95379638671875], [288.8173828125, 100.29998779296875], [128.3466796875, 100.29998779296875]]}, {"title": "1.3 What is debugging?", "heading_level": null, "page_id": 24, "polygon": [[128.0478515625, 439.2318420410156], [292.5728454589844, 439.2318420410156], [292.5728454589844, 453.57806396484375], [128.0478515625, 453.57806396484375]]}, {"title": "1.3.1 Syntax errors", "heading_level": null, "page_id": 24, "polygon": [[129.09375, 548.9158477783203], [237.53152465820312, 548.9158477783203], [237.53152465820312, 560.8710479736328], [129.09375, 560.8710479736328]]}, {"title": "1.3.2 Runtime errors", "heading_level": null, "page_id": 25, "polygon": [[85.53955078125, 86.3349609375], [207.*********, 86.3349609375], [207.*********, 99.24493408203125], [85.53955078125, 99.24493408203125]]}, {"title": "1.3.3 Semantic errors", "heading_level": null, "page_id": 25, "polygon": [[85.9130859375, 206.12109375], [208.1337890625, 206.12109375], [208.1337890625, 220.16192626953125], [85.9130859375, 220.16192626953125]]}, {"title": "1.3.4 Experimental debugging", "heading_level": null, "page_id": 25, "polygon": [[85.83837890625, 364.*********], [258.78515625, 364.*********], [258.78515625, 377.6620178222656], [85.83837890625, 377.6620178222656]]}, {"title": "1.4 Formal and natural languages", "heading_level": null, "page_id": 26, "polygon": [[127.*********75, 85.95379638671875], [357.099609375, 85.95379638671875], [357.099609375, 100.353515625], [127.*********75, 100.353515625]]}, {"title": "Programming languages are formal languages that have been designed to\nexpress computations.", "heading_level": null, "page_id": 26, "polygon": [[153.59765625, 224.490234375], [500.8359375, 224.490234375], [500.8359375, 246.73370361328125], [153.59765625, 246.73370361328125]]}, {"title": "1.5 The first program", "heading_level": null, "page_id": 27, "polygon": [[85.9130859375, 447.3996887207031], [232.6595001220703, 447.3996887207031], [232.6595001220703, 461.74591064453125], [85.9130859375, 461.74591064453125]]}, {"title": "1.6 Debugging", "heading_level": null, "page_id": 28, "polygon": [[128.42138671875, 85.95379638671875], [236.00576782226562, 85.95379638671875], [236.00576782226562, 101.2*********], [128.42138671875, 101.2*********]]}, {"title": "1.7 Glossary", "heading_level": null, "page_id": 28, "polygon": [[128.0478515625, 502.734375], [220.0527801513672, 502.734375], [220.0527801513672, 517.5260620117188], [128.0478515625, 517.5260620117188]]}, {"title": "1.8 Exercises", "heading_level": null, "page_id": 30, "polygon": [[128.6455078125, 85.63177490234375], [221.63088989257812, 85.63177490234375], [221.63088989257812, 99.97796630859375], [128.6455078125, 99.97796630859375]]}, {"title": "Chapter 2", "heading_level": null, "page_id": 32, "polygon": [[128.86962890625, 164.935546875], [221.1328125, 164.935546875], [221.1328125, 185.71087646484375], [128.86962890625, 185.71087646484375]]}, {"title": "Variables, expressions and\nstatements", "heading_level": null, "page_id": 32, "polygon": [[128.9443359375, 219.462890625], [430.3125, 219.462890625], [430.3125, 276.60333251953125], [128.9443359375, 276.60333251953125]]}, {"title": "2.1 Values and types", "heading_level": null, "page_id": 32, "polygon": [[128.*********, 326.390625], [273.427734375, 326.390625], [273.427734375, 340.7639465332031], [128.*********, 340.7639465332031]]}, {"title": "2.2 Variables", "heading_level": null, "page_id": 33, "polygon": [[85.9130859375, 272.830078125], [180.03765869140625, 272.830078125], [180.03765869140625, 288.0208435058594], [85.9130859375, 288.0208435058594]]}, {"title": "2.3 Variable names and keywords", "heading_level": null, "page_id": 33, "polygon": [[85.98779296875, 607.3946685791016], [315.92486572265625, 607.3946685791016], [315.92486572265625, 621.7408599853516], [85.98779296875, 621.7408599853516]]}, {"title": "2.4 Operators and operands", "heading_level": null, "page_id": 34, "polygon": [[128.197265625, 452.3846435546875], [319.6728820800781, 452.3846435546875], [319.6728820800781, 466.7308654785156], [128.197265625, 466.7308654785156]]}, {"title": "2.5 Expressions and statements", "heading_level": null, "page_id": 35, "polygon": [[85.6142578125, 198.7734375], [298.7524108886719, 198.7734375], [298.7524108886719, 213.87689208984375], [85.6142578125, 213.87689208984375]]}, {"title": "2.6 Interactive mode and script mode", "heading_level": null, "page_id": 35, "polygon": [[85.46484375, 400.640625], [337.83148193359375, 400.640625], [337.83148193359375, 415.3359375], [85.46484375, 415.3359375]]}, {"title": "2.7 Order of operations", "heading_level": null, "page_id": 36, "polygon": [[128.42138671875, 295.453125], [291.357421875, 295.453125], [291.357421875, 311.4539*********], [128.42138671875, 311.4539*********]]}, {"title": "2.8 String operations", "heading_level": null, "page_id": 36, "polygon": [[127.*********75, 633.83203125], [275.4434814453125, 633.83203125], [275.4434814453125, 649.3340301513672], [127.*********75, 649.3340301513672]]}, {"title": "2.9 Comments", "heading_level": null, "page_id": 37, "polygon": [[86.13720703125, 291.005859375], [189.60665893554688, 291.005859375], [189.60665893554688, 306.1039123535156], [86.13720703125, 306.1039123535156]]}, {"title": "2.10 Debugging", "heading_level": null, "page_id": 37, "polygon": [[85.53955078125, 640.79296875], [201.41015625, 640.79296875], [201.41015625, 655.48828125], [85.53955078125, 655.48828125]]}, {"title": "2.11 Glossary", "heading_level": null, "page_id": 38, "polygon": [[128.3466796875, 389.354736328125], [227.2259063720703, 389.354736328125], [227.2259063720703, 403.7009582519531], [128.3466796875, 403.7009582519531]]}, {"title": "2.12 Exercises", "heading_level": null, "page_id": 39, "polygon": [[85.39013671875, 297.7734375], [185.60400390625, 297.7734375], [185.60400390625, 312.24993896484375], [85.39013671875, 312.24993896484375]]}, {"title": "Chapter 3", "heading_level": null, "page_id": 40, "polygon": [[128.27197265625, 164.6455078125], [220.84561157226562, 164.6455078125], [220.84561157226562, 185.87091064453125], [128.27197265625, 185.87091064453125]]}, {"title": "Functions", "heading_level": null, "page_id": 40, "polygon": [[128.49609375, 221.65228271484375], [242.349609375, 221.65228271484375], [242.349609375, 246.4393310546875], [128.49609375, 246.4393310546875]]}, {"title": "3.1 Function calls", "heading_level": null, "page_id": 40, "polygon": [[128.86962890625, 296.033203125], [253.1064453125, 296.033203125], [253.1064453125, 310.75994873046875], [128.86962890625, 310.75994873046875]]}, {"title": "3.2 Type conversion functions", "heading_level": null, "page_id": 40, "polygon": [[127.8984375, 495.38671875], [335.12371826171875, 495.38671875], [335.12371826171875, 510.4379577636719], [127.8984375, 510.4379577636719]]}, {"title": "3.3 Math functions", "heading_level": null, "page_id": 41, "polygon": [[85.83837890625, 231.064453125], [219.475341796875, 231.064453125], [219.475341796875, 246.52093505859375], [85.83837890625, 246.52093505859375]]}, {"title": "3.4 Composition", "heading_level": null, "page_id": 42, "polygon": [[129.09375, 85.63177490234375], [247.15277099609375, 85.63177490234375], [247.15277099609375, 99.97796630859375], [129.09375, 99.97796630859375]]}, {"title": "3.5 Adding new functions", "heading_level": null, "page_id": 42, "polygon": [[128.197265625, 341.8738098144531], [309.3005065917969, 341.8738098144531], [309.3005065917969, 356.22003173828125], [128.197265625, 356.22003173828125]]}, {"title": "3.6 Definitions and uses", "heading_level": null, "page_id": 43, "polygon": [[85.98779296875, 487.65234375], [253.33245849609375, 487.65234375], [253.33245849609375, 502.0660705566406], [85.98779296875, 502.0660705566406]]}, {"title": "3.7 Flow of execution", "heading_level": null, "page_id": 44, "polygon": [[127.8984375, 188.74676513671875], [278.2409973144531, 188.74676513671875], [278.2409973144531, 203.09295654296875], [127.8984375, 203.09295654296875]]}, {"title": "3.8 Parameters and arguments", "heading_level": null, "page_id": 44, "polygon": [[128.27197265625, 519.7107849121094], [335.6114196777344, 519.7107849121094], [335.6114196777344, 534.0570068359375], [128.27197265625, 534.0570068359375]]}, {"title": "3.9 Variables and parameters are local", "heading_level": null, "page_id": 45, "polygon": [[85.763671875, 466.3828125], [342.60882568359375, 466.3828125], [342.60882568359375, 480.94195556640625], [85.763671875, 480.94195556640625]]}, {"title": "3.10 <PERSON>ack diagrams", "heading_level": null, "page_id": 46, "polygon": [[128.3466796875, 294.8467102050781], [269.0947265625, 294.8467102050781], [269.0947265625, 309.19293212890625], [128.3466796875, 309.19293212890625]]}, {"title": "3.11 Fruitful functions and void functions", "heading_level": null, "page_id": 47, "polygon": [[85.3154296875, 139.025390625], [369.6944274902344, 139.025390625], [369.6944274902344, 154.137939453125], [85.3154296875, 154.137939453125]]}, {"title": "3.12 Why functions?", "heading_level": null, "page_id": 47, "polygon": [[85.3154296875, 584.71875], [229.04421997070312, 584.71875], [229.04421997070312, 599.4089813232422], [85.3154296875, 599.4089813232422]]}, {"title": "3.13 Importing with from", "heading_level": null, "page_id": 48, "polygon": [[127.37548828125, 168.674072265625], [302.5994873046875, 168.674072265625], [302.5994873046875, 185.096923828125], [127.37548828125, 185.096923828125]]}, {"title": "3.14 Debugging", "heading_level": null, "page_id": 48, "polygon": [[128.12255859375, 565.5196228027344], [243.24609375, 565.5196228027344], [243.24609375, 579.8658142089844], [128.12255859375, 579.8658142089844]]}, {"title": "3.15 Glossary", "heading_level": null, "page_id": 49, "polygon": [[85.83837890625, 186.78515625], [184.02589416503906, 186.78515625], [184.02589416503906, 201.30694580078125], [85.83837890625, 201.30694580078125]]}, {"title": "3.16 Exercises", "heading_level": null, "page_id": 50, "polygon": [[128.3466796875, 199.51275634765625], [228.80398559570312, 199.51275634765625], [228.80398559570312, 213.85894*********], [128.3466796875, 213.85894*********]]}, {"title": "Chapter 4", "heading_level": null, "page_id": 52, "polygon": [[128.6455078125, 165.43450927734375], [221.2822265625, 165.43450927734375], [221.2822265625, 186.09698486328125], [128.6455078125, 186.09698486328125]]}, {"title": "Case study: interface design", "heading_level": null, "page_id": 52, "polygon": [[128.6455078125, 222.10333251953125], [448.83984375, 222.10333251953125], [448.83984375, 246.890380859375], [128.6455078125, 246.890380859375]]}, {"title": "4.1 TurtleWorld", "heading_level": null, "page_id": 52, "polygon": [[128.*********, 351.9140625], [241.05563354492188, 351.9140625], [241.05563354492188, 366.4170227050781], [128.*********, 366.4170227050781]]}, {"title": "4.2 Simple repetition", "heading_level": null, "page_id": 53, "polygon": [[85.53955078125, 451.5947570800781], [233.82159423828125, 451.5947570800781], [233.82159423828125, 465.94097900390625], [85.53955078125, 465.94097900390625]]}, {"title": "4.3 Exercises", "heading_level": null, "page_id": 54, "polygon": [[128.0478515625, 382.1136779785156], [222.1787109375, 382.1136779785156], [222.1787109375, 396.45989990234375], [128.0478515625, 396.45989990234375]]}, {"title": "4.4 Encapsulation", "heading_level": null, "page_id": 55, "polygon": [[85.6142578125, 224.876953125], [211.9005126953125, 224.876953125], [211.9005126953125, 239.701904296875], [85.6142578125, 239.701904296875]]}, {"title": "4.5 Generalization", "heading_level": null, "page_id": 55, "polygon": [[85.763671875, 546.046875], [216.69219970703125, 546.046875], [216.69219970703125, 560.9458923339844], [85.763671875, 560.9458923339844]]}, {"title": "4.6 Interface design", "heading_level": null, "page_id": 56, "polygon": [[128.49609375, 361.9498291015625], [267.4669494628906, 361.9498291015625], [267.4669494628906, 376.2960510253906], [128.49609375, 376.2960510253906]]}, {"title": "4.7 Refactoring", "heading_level": null, "page_id": 57, "polygon": [[85.46484375, 244.79296875], [195.18724060058594, 244.79296875], [195.18724060058594, 260.310791015625], [85.46484375, 260.310791015625]]}, {"title": "4.8 A development plan", "heading_level": null, "page_id": 58, "polygon": [[128.6455078125, 263.548828125], [295.9892578125, 263.548828125], [295.9892578125, 278.6109619140625], [128.6455078125, 278.6109619140625]]}, {"title": "4.9 docstring", "heading_level": null, "page_id": 58, "polygon": [[127.97314453125, 558.4587707519531], [223.2376251220703, 558.4587707519531], [223.2376251220703, 572.8049621582031], [127.97314453125, 572.8049621582031]]}, {"title": "4.10 Debugging", "heading_level": null, "page_id": 59, "polygon": [[85.46484375, 240.56890869140625], [201.2607421875, 240.56890869140625], [201.2607421875, 255.234375], [85.46484375, 255.234375]]}, {"title": "4.11 Glossary", "heading_level": null, "page_id": 59, "polygon": [[85.46484375, 474.890625], [184.0259246826172, 474.890625], [184.0259246826172, 489.46612548828125], [85.46484375, 489.46612548828125]]}, {"title": "4.12 Exercises", "heading_level": null, "page_id": 60, "polygon": [[127.8984375, 445.11328125], [228.80401611328125, 445.11328125], [228.80401611328125, 459.5309753417969], [127.8984375, 459.5309753417969]]}, {"title": "Chapter 5", "heading_level": null, "page_id": 62, "polygon": [[129.01904296875, 162.37750244140625], [220.84561157226562, 162.37750244140625], [220.84561157226562, 183.111328125], [129.01904296875, 183.111328125]]}, {"title": "Conditionals and recursion", "heading_level": null, "page_id": 62, "polygon": [[128.3466796875, 215.982421875], [438.6796875, 215.982421875], [438.6796875, 240.*********], [128.3466796875, 240.*********]]}, {"title": "5.1 Modulus operator", "heading_level": null, "page_id": 62, "polygon": [[128.0478515625, 287.9187927246094], [280.2350769042969, 287.9187927246094], [280.2350769042969, 302.2650146484375], [128.0478515625, 302.2650146484375]]}, {"title": "5.2 Boolean expressions", "heading_level": null, "page_id": 62, "polygon": [[128.57080078125, 524.77734375], [295.3703918457031, 524.77734375], [295.3703918457031, 539.4530181884766], [128.57080078125, 539.4530181884766]]}, {"title": "5.3 Logical operators", "heading_level": null, "page_id": 63, "polygon": [[85.763671875, 240.15234375], [231.890625, 240.15234375], [231.890625, 254.84765625], [85.763671875, 254.84765625]]}, {"title": "5.4 Conditional execution", "heading_level": null, "page_id": 63, "polygon": [[85.83837890625, 471.796875], [264.1640625, 471.796875], [264.1640625, 487.25201416015625], [85.83837890625, 487.25201416015625]]}, {"title": "5.5 Alternative execution", "heading_level": null, "page_id": 64, "polygon": [[128.*********, 85.95379638671875], [302.712890625, 85.95379638671875], [302.712890625, 100.29998779296875], [128.*********, 100.29998779296875]]}, {"title": "5.6 Chained conditionals", "heading_level": null, "page_id": 64, "polygon": [[128.6455078125, 282.69140625], [302.5634765625, 282.69140625], [302.5634765625, 297.6069641113281], [128.6455078125, 297.6069641113281]]}, {"title": "5.7 Nested conditionals", "heading_level": null, "page_id": 64, "polygon": [[128.42138671875, 597.8671875], [292.94586181640625, 597.8671875], [292.94586181640625, 613.0500030517578], [128.42138671875, 613.0500030517578]]}, {"title": "5.8 Recursion", "heading_level": null, "page_id": 65, "polygon": [[85.9130859375, 372.603515625], [184.81497192382812, 372.603515625], [184.81497192382812, 387.2398681640625], [85.9130859375, 387.2398681640625]]}, {"title": "5.9 <PERSON>ack diagrams for recursive functions", "heading_level": null, "page_id": 66, "polygon": [[128.12255859375, 468.1249084472656], [413.6979064941406, 468.1249084472656], [413.6979064941406, 482.47113037109375], [128.12255859375, 482.47113037109375]]}, {"title": "5.10 Infinite recursion", "heading_level": null, "page_id": 67, "polygon": [[85.46484375, 251.61279296875], [239.4022216796875, 251.61279296875], [239.4022216796875, 265.958984375], [85.46484375, 265.958984375]]}, {"title": "5.11 Keyboard input", "heading_level": null, "page_id": 67, "polygon": [[85.53955078125, 538.5088043212891], [229.8620147705078, 538.5088043212891], [229.8620147705078, 552.8549957275391], [85.53955078125, 552.8549957275391]]}, {"title": "5.12 Debugging", "heading_level": null, "page_id": 68, "polygon": [[127.8984375, 428.7655944824219], [243.17886352539062, 428.7655944824219], [243.17886352539062, 443.1796875], [127.8984375, 443.1796875]]}, {"title": "5.13 Glossary", "heading_level": null, "page_id": 69, "polygon": [[86.13720703125, 427.601806640625], [184.0259246826172, 427.601806640625], [184.0259246826172, 441.9480285644531], [86.13720703125, 441.9480285644531]]}, {"title": "5.14 Exercises", "heading_level": null, "page_id": 70, "polygon": [[128.9443359375, 235.6197509765625], [228.80401611328125, 235.6197509765625], [228.80401611328125, 249.9659423828125], [128.9443359375, 249.9659423828125]]}, {"title": "Chapter 6", "heading_level": null, "page_id": 72, "polygon": [[128.197265625, 165.4189453125], [220.84561157226562, 165.4189453125], [220.84561157226562, 186.46197509765625], [128.197265625, 186.46197509765625]]}, {"title": "Fruitful functions", "heading_level": null, "page_id": 72, "polygon": [[127.001953125, 222.169921875], [331.2926330566406, 222.169921875], [331.2926330566406, 247.620361328125], [127.001953125, 247.620361328125]]}, {"title": "6.1 Return values", "heading_level": null, "page_id": 72, "polygon": [[128.49609375, 297.7734375], [253.705078125, 297.7734375], [253.705078125, 312.531982421875], [128.49609375, 312.531982421875]]}, {"title": "6.2 Incremental development", "heading_level": null, "page_id": 73, "polygon": [[85.39013671875, 373.40087890625], [288.0703125, 373.40087890625], [288.0703125, 387.7471008300781], [85.39013671875, 387.7471008300781]]}, {"title": "6.3 Composition", "heading_level": null, "page_id": 75, "polygon": [[86.2119140625, 230.725830078125], [203.9527587890625, 230.725830078125], [203.9527587890625, 245.072021484375], [86.2119140625, 245.072021484375]]}, {"title": "6.4 Boolean functions", "heading_level": null, "page_id": 75, "polygon": [[85.0*********, 584.7738647460938], [237.82412719726562, 584.7738647460938], [237.82412719726562, 599.1200561523438], [85.0*********, 599.1200561523438]]}, {"title": "6.5 More recursion", "heading_level": null, "page_id": 76, "polygon": [[128.49609375, 388.5017395019531], [261.9006652832031, 388.5017395019531], [261.9006652832031, 402.84796142578125], [128.49609375, 402.84796142578125]]}, {"title": "6.6 Leap of faith", "heading_level": null, "page_id": 78, "polygon": [[128.27197265625, 251.32977294921875], [245.56033325195312, 251.32977294921875], [245.56033325195312, 265.67596435546875], [128.27197265625, 265.67596435546875]]}, {"title": "6.7 One more example", "heading_level": null, "page_id": 78, "polygon": [[128.3466796875, 562.67578125], [286.1279296875, 562.67578125], [286.1279296875, 577.8719329833984], [128.3466796875, 577.8719329833984]]}, {"title": "6.8 Checking types", "heading_level": null, "page_id": 79, "polygon": [[85.6142578125, 239.4217529296875], [220.27874755859375, 239.4217529296875], [220.27874755859375, 253.7679443359375], [85.6142578125, 253.7679443359375]]}, {"title": "6.9 Debugging", "heading_level": null, "page_id": 80, "polygon": [[128.3466796875, 226.3597412109375], [236.5224609375, 226.3597412109375], [236.5224609375, 240.92578125], [128.3466796875, 240.92578125]]}, {"title": "6.10 Glossary", "heading_level": null, "page_id": 81, "polygon": [[86.39997863769531, 302.02734375], [184.02586364746094, 302.02734375], [184.02586364746094, 316.90087890625], [86.39997863769531, 316.90087890625]]}, {"title": "6.11 Exercises", "heading_level": null, "page_id": 81, "polygon": [[85.83837890625, 538.4977264404297], [185.60397338867188, 538.4977264404297], [185.60397338867188, 552.8439178466797], [85.83837890625, 552.8439178466797]]}, {"title": "Chapter 7", "heading_level": null, "page_id": 84, "polygon": [[128.*********, 163.99639892578125], [221.73046875, 163.99639892578125], [221.73046875, 184.65887451171875], [128.*********, 184.65887451171875]]}, {"title": "Iteration", "heading_level": null, "page_id": 84, "polygon": [[128.86962890625, 219.22723388671875], [227.408203125, 219.22723388671875], [227.408203125, 244.0142822265625], [128.86962890625, 244.0142822265625]]}, {"title": "7.1 Multiple assignment", "heading_level": null, "page_id": 84, "polygon": [[128.72021484375, 292.74609375], [299.126953125, 292.74609375], [299.126953125, 307.1228942871094], [128.72021484375, 307.1228942871094]]}, {"title": "7.2 Updating variables", "heading_level": null, "page_id": 85, "polygon": [[85.763671875, 163.58203125], [244.19384765625, 163.58203125], [244.19384765625, 178.75494384765625], [85.763671875, 178.75494384765625]]}, {"title": "7.3 The while statement", "heading_level": null, "page_id": 85, "polygon": [[85.9130859375, 421.3271179199219], [251.50521850585938, 421.3271179199219], [251.50521850585938, 437.7499694824219], [85.9130859375, 437.7499694824219]]}, {"title": "7.4 break", "heading_level": null, "page_id": 86, "polygon": [[127.599609375, 614.340087890625], [198.31837463378906, 614.340087890625], [198.31837463378906, 630.7629089355469], [127.599609375, 630.7629089355469]]}, {"title": "7.5 Square roots", "heading_level": null, "page_id": 87, "polygon": [[85.83837890625, 377.9056091308594], [201.16961669921875, 377.9056091308594], [201.16961669921875, 392.2518310546875], [85.83837890625, 392.2518310546875]]}, {"title": "7.6 Algorithms", "heading_level": null, "page_id": 88, "polygon": [[128.12255859375, 594.0], [236.7947998046875, 594.0], [236.7947998046875, 608.9769439697266], [128.12255859375, 608.9769439697266]]}, {"title": "7.7 Debugging", "heading_level": null, "page_id": 89, "polygon": [[85.53955078125, 341.0859375], [193.04296875, 341.0859375], [193.04296875, 357.71484375], [85.53955078125, 357.71484375]]}, {"title": "7.8 Glossary", "heading_level": null, "page_id": 89, "polygon": [[85.68896484375, 653.94140625], [177.205078125, 653.94140625], [177.205078125, 669.645866394043], [85.68896484375, 669.645866394043]]}, {"title": "7.9 Exercises", "heading_level": null, "page_id": 90, "polygon": [[128.49609375, 248.41668701171875], [221.73046875, 248.41668701171875], [221.73046875, 262.76287841796875], [128.49609375, 262.76287841796875]]}, {"title": "Chapter 8", "heading_level": null, "page_id": 92, "polygon": [[128.197265625, 164.62750244140625], [220.84561157226562, 164.62750244140625], [220.84561157226562, 185.28997802734375], [128.197265625, 185.28997802734375]]}, {"title": "Strings", "heading_level": null, "page_id": 92, "polygon": [[129.2431640625, 220.4903564453125], [211.1220703125, 220.4903564453125], [211.1220703125, 245.27740478515625], [129.2431640625, 245.27740478515625]]}, {"title": "8.1 A string is a sequence", "heading_level": null, "page_id": 92, "polygon": [[128.6455078125, 294.6708068847656], [304.5088806152344, 294.6708068847656], [304.5088806152344, 309.01702880859375], [128.6455078125, 309.01702880859375]]}, {"title": "8.2 len", "heading_level": null, "page_id": 92, "polygon": [[127.67431640625, 661.3962249755859], [183.7425537109375, 661.3962249755859], [183.7425537109375, 677.8190460205078], [127.67431640625, 677.8190460205078]]}, {"title": "8.3 Traversal with a for loop", "heading_level": null, "page_id": 93, "polygon": [[85.9130859375, 339.75921630859375], [281.5877*********, 339.75921630859375], [281.5877*********, 356.18206787109375], [85.9130859375, 356.18206787109375]]}, {"title": "8.4 String slices", "heading_level": null, "page_id": 94, "polygon": [[128.86962890625, 401.4140625], [241.945068359375, 401.4140625], [241.945068359375, 415.7750244140625], [128.86962890625, 415.7750244140625]]}, {"title": "8.5 Strings are immutable", "heading_level": null, "page_id": 95, "polygon": [[85.46484375, 150.43359375], [264.5080871582031, 150.43359375], [264.5080871582031, 165.4459228515625], [85.46484375, 165.4459228515625]]}, {"title": "8.6 Searching", "heading_level": null, "page_id": 95, "polygon": [[86.13720703125, 416.109375], [184.02597045898438, 416.109375], [184.02597045898438, 430.8046875], [86.13720703125, 430.8046875]]}, {"title": "8.7 Looping and counting", "heading_level": null, "page_id": 96, "polygon": [[127.*********75, 85.95379638671875], [307.494140625, 85.95379638671875], [307.494140625, 100.546875], [127.*********75, 100.546875]]}, {"title": "8.8 String methods", "heading_level": null, "page_id": 96, "polygon": [[128.3466796875, 318.9787292480469], [262.68975830078125, 318.9787292480469], [262.68975830078125, 333.324951171875], [128.3466796875, 333.324951171875]]}, {"title": "8.9 The in operator", "heading_level": null, "page_id": 97, "polygon": [[85.9130859375, 341.6040344238281], [220.9252471923828, 341.6040344238281], [220.9252471923828, 358.0268859863281], [85.9130859375, 358.0268859863281]]}, {"title": "8.10 String comparison", "heading_level": null, "page_id": 97, "polygon": [[85.9130859375, 664.7117080688477], [246.9814453125, 664.7117080688477], [246.9814453125, 679.0579071044922], [85.9130859375, 679.0579071044922]]}, {"title": "8.11 Debugging", "heading_level": null, "page_id": 98, "polygon": [[127.97314453125, 319.0566101074219], [243.17892456054688, 319.0566101074219], [243.17892456054688, 333.40283203125], [127.97314453125, 333.40283203125]]}, {"title": "8.12 Glossary", "heading_level": null, "page_id": 99, "polygon": [[85.763671875, 654.71484375], [184.02589416503906, 654.71484375], [184.02589416503906, 669.3321151733398], [85.763671875, 669.3321151733398]]}, {"title": "8.13 Exercises", "heading_level": null, "page_id": 100, "polygon": [[128.9443359375, 389.08367919921875], [228.80398559570312, 389.08367919921875], [228.80398559570312, 403.4299011230469], [128.9443359375, 403.4299011230469]]}, {"title": "Chapter 9", "heading_level": null, "page_id": 102, "polygon": [[129.16845703125, 162.73150634765625], [220.84561157226562, 162.73150634765625], [220.84561157226562, 183.39398193359375], [129.16845703125, 183.39398193359375]]}, {"title": "Case study: word play", "heading_level": null, "page_id": 102, "polygon": [[128.42138671875, 216.69732666015625], [381.684814453125, 216.69732666015625], [381.684814453125, 241.484375], [128.42138671875, 241.484375]]}, {"title": "9.1 Reading word lists", "heading_level": null, "page_id": 102, "polygon": [[128.27197265625, 288.9817810058594], [285.3853759765625, 288.9817810058594], [285.3853759765625, 303.3280029296875], [128.27197265625, 303.3280029296875]]}, {"title": "9.2 Exercises", "heading_level": null, "page_id": 103, "polygon": [[85.6142578125, 264.12890625], [178.430908203125, 264.12890625], [178.430908203125, 279.781005859375], [85.6142578125, 279.781005859375]]}, {"title": "9.3 Search", "heading_level": null, "page_id": 103, "polygon": [[86.0625, 652.78125], [162.50668334960938, 652.78125], [162.50668334960938, 667.7410888671875], [86.0625, 667.7410888671875]]}, {"title": "9.4 Looping with indices", "heading_level": null, "page_id": 104, "polygon": [[127.37548828125, 652.4447631835938], [301.3241271972656, 652.4447631835938], [301.3241271972656, 666.7909545898438], [127.37548828125, 666.7909545898438]]}, {"title": "9.5 Debugging", "heading_level": null, "page_id": 106, "polygon": [[128.0478515625, 194.90625], [236.00576782226562, 194.90625], [236.00576782226562, 209.6015625], [128.0478515625, 209.6015625]]}, {"title": "9.6 Glossary", "heading_level": null, "page_id": 106, "polygon": [[128.72021484375, 590.9436798095703], [220.05284118652344, 590.9436798095703], [220.05284118652344, 605.2898712158203], [128.72021484375, 605.2898712158203]]}, {"title": "9.7 Exercises", "heading_level": null, "page_id": 107, "polygon": [[85.6142578125, 85.271484375], [179.4462890625, 85.271484375], [179.4462890625, 99.97796630859375], [85.6142578125, 99.97796630859375]]}, {"title": "Chapter 10", "heading_level": null, "page_id": 108, "polygon": [[128.49609375, 163.1953125], [232.787109375, 163.1953125], [232.787109375, 184.333984375], [128.49609375, 184.333984375]]}, {"title": "Lists", "heading_level": null, "page_id": 108, "polygon": [[127.7490234375, 218.57733154296875], [184.2275390625, 218.57733154296875], [184.2275390625, 243.3643798828125], [127.7490234375, 243.3643798828125]]}, {"title": "10.1 A list is a sequence", "heading_level": null, "page_id": 108, "polygon": [[128.57080078125, 291.8017883300781], [294.1365661621094, 291.8017883300781], [294.1365661621094, 306.14801025390625], [128.57080078125, 306.14801025390625]]}, {"title": "10.2 Lists are mutable", "heading_level": null, "page_id": 108, "polygon": [[127.4501953125, 612.3318481445312], [281.39727783203125, 612.3318481445312], [281.39727783203125, 626.6780395507812], [127.4501953125, 626.6780395507812]]}, {"title": "10.3 Traversing a list", "heading_level": null, "page_id": 110, "polygon": [[128.6455078125, 85.95379638671875], [275.6689453125, 85.95379638671875], [275.6689453125, 100.29998779296875], [128.6455078125, 100.29998779296875]]}, {"title": "10.4 List operations", "heading_level": null, "page_id": 110, "polygon": [[129.01904296875, 423.2647705078125], [266.66363525390625, 423.2647705078125], [266.66363525390625, 437.6109924316406], [129.01904296875, 437.6109924316406]]}, {"title": "10.5 List slices", "heading_level": null, "page_id": 110, "polygon": [[127.1513671875, 662.3668518066406], [233.2353515625, 662.3668518066406], [233.2353515625, 676.7130432128906], [127.1513671875, 676.7130432128906]]}, {"title": "10.6 List methods", "heading_level": null, "page_id": 111, "polygon": [[85.46484375, 379.4886779785156], [210.70993041992188, 379.4886779785156], [210.70993041992188, 393.83489990234375], [85.46484375, 393.83489990234375]]}, {"title": "10.7 Map, filter and reduce", "heading_level": null, "page_id": 112, "polygon": [[128.0478515625, 85.95379638671875], [313.76953125, 85.95379638671875], [313.76953125, 100.29998779296875], [128.0478515625, 100.29998779296875]]}, {"title": "10.8 Deleting elements", "heading_level": null, "page_id": 113, "polygon": [[85.6142578125, 272.443359375], [245.77203369140625, 272.443359375], [245.77203369140625, 287.138671875], [85.6142578125, 287.138671875]]}, {"title": "10.9 Lists and strings", "heading_level": null, "page_id": 114, "polygon": [[128.6455078125, 85.95379638671875], [277.4619140625, 85.95379638671875], [277.4619140625, 100.29998779296875], [128.6455078125, 100.29998779296875]]}, {"title": "10.10 Objects and values", "heading_level": null, "page_id": 114, "polygon": [[127.8984375, 551.84765625], [300.5208435058594, 551.84765625], [300.5208435058594, 566.5540924072266], [127.8984375, 566.5540924072266]]}, {"title": "10.11 Aliasing", "heading_level": null, "page_id": 115, "polygon": [[85.6142578125, 513.5625], [189.0087890625, 513.5625], [189.0087890625, 528.2919616699219], [85.6142578125, 528.2919616699219]]}, {"title": "10.12 List arguments", "heading_level": null, "page_id": 116, "polygon": [[128.197265625, 420.4227600097656], [273.836669921875, 420.4227600097656], [273.836669921875, 434.76898193359375], [128.197265625, 434.76898193359375]]}, {"title": "10.13 Debugging", "heading_level": null, "page_id": 117, "polygon": [[85.6142578125, 428.7527160644531], [207.15200805664062, 428.7527160644531], [207.15200805664062, 443.56640625], [85.6142578125, 443.56640625]]}, {"title": "10.14 Glossary", "heading_level": null, "page_id": 118, "polygon": [[129.2431640625, 471.78265380859375], [234.4306640625, 471.78265380859375], [234.4306640625, 486.1288757324219], [129.2431640625, 486.1288757324219]]}, {"title": "10.15 Exercises", "heading_level": null, "page_id": 119, "polygon": [[85.6142578125, 292.359375], [192.77706909179688, 292.359375], [192.77706909179688, 307.092041015625], [85.6142578125, 307.092041015625]]}, {"title": "Chapter 11", "heading_level": null, "page_id": 122, "polygon": [[128.9443359375, 162.3251953125], [232.3388671875, 162.3251953125], [232.3388671875, 183.33795*********], [128.9443359375, 183.33795*********]]}, {"title": "Dictionaries", "heading_level": null, "page_id": 122, "polygon": [[129.16845703125, 216.5625], [268.646484375, 216.5625], [268.646484375, 241.371337890625], [129.16845703125, 241.371337890625]]}, {"title": "11.1 Dictionary as a set of counters", "heading_level": null, "page_id": 123, "polygon": [[85.46484375, 555.328125], [322.28021240234375, 555.328125], [322.28021240234375, 569.7739410400391], [85.46484375, 569.7739410400391]]}, {"title": "11.2 Looping and dictionaries", "heading_level": null, "page_id": 124, "polygon": [[127.4501953125, 653.94140625], [333.2156982421875, 653.94140625], [333.2156982421875, 668.4161911010742], [127.4501953125, 668.4161911010742]]}, {"title": "11.3 <PERSON>erse lookup", "heading_level": null, "page_id": 125, "polygon": [[85.46484375, 326.1167297363281], [228.26959228515625, 326.1167297363281], [228.26959228515625, 340.46295*********], [85.46484375, 340.46295*********]]}, {"title": "11.4 Dictionaries and lists", "heading_level": null, "page_id": 126, "polygon": [[127.30078125, 337.6007995605469], [308.4684143066406, 337.6007995605469], [308.4684143066406, 351.947021484375], [127.30078125, 351.947021484375]]}, {"title": "11.5 Memos", "heading_level": null, "page_id": 127, "polygon": [[85.763671875, 653.16796875], [174.4713134765625, 653.16796875], [174.4713134765625, 668.0169982910156], [85.763671875, 668.0169982910156]]}, {"title": "11.6 Global variables", "heading_level": null, "page_id": 129, "polygon": [[85.3154296875, 84.73974609375], [234.7294921875, 84.73974609375], [234.7294921875, 100.29998779296875], [85.3154296875, 100.29998779296875]]}, {"title": "11.7 Long integers", "heading_level": null, "page_id": 130, "polygon": [[127.7490234375, 234.62371826171875], [259.2333984375, 234.62371826171875], [259.2333984375, 249.43359375], [127.7490234375, 249.43359375]]}, {"title": "11.8 Debugging", "heading_level": null, "page_id": 130, "polygon": [[127.52490234375, 561.90234375], [243.17897033691406, 561.90234375], [243.17897033691406, 576.4298858642578], [127.52490234375, 576.4298858642578]]}, {"title": "11.9 Glossary", "heading_level": null, "page_id": 131, "polygon": [[85.68896484375, 335.1865539550781], [184.02589416503906, 335.1865539550781], [184.02589416503906, 349.53277587890625], [85.68896484375, 349.53277587890625]]}, {"title": "11.10 Exercises", "heading_level": null, "page_id": 132, "polygon": [[128.3466796875, 180.6627197265625], [236.5224609375, 180.6627197265625], [236.5224609375, 195.0089111328125], [128.3466796875, 195.0089111328125]]}, {"title": "Chapter 12", "heading_level": null, "page_id": 134, "polygon": [[128.9443359375, 162.615234375], [232.6376953125, 162.615234375], [232.6376953125, 183.574951171875], [128.9443359375, 183.574951171875]]}, {"title": "<PERSON><PERSON>", "heading_level": null, "page_id": 134, "polygon": [[128.6455078125, 217.059326171875], [204.87843322753906, 217.059326171875], [204.87843322753906, 241.84637451171875], [128.6455078125, 241.84637451171875]]}, {"title": "12.1 Tuples are immutable", "heading_level": null, "page_id": 134, "polygon": [[128.27197265625, 289.458984375], [311.4381103515625, 289.458984375], [311.4381103515625, 303.8710021972656], [128.27197265625, 303.8710021972656]]}, {"title": "12.2 <PERSON><PERSON> assignment", "heading_level": null, "page_id": 135, "polygon": [[85.6142578125, 315.5625], [241.53985595703125, 315.5625], [241.53985595703125, 330.0780029296875], [85.6142578125, 330.0780029296875]]}, {"title": "12.3 Tuples as return values", "heading_level": null, "page_id": 136, "polygon": [[128.86962890625, 85.95379638671875], [319.80194091796875, 85.95379638671875], [319.80194091796875, 100.29998779296875], [128.86962890625, 100.29998779296875]]}, {"title": "12.4 Variable-length argument tuples", "heading_level": null, "page_id": 136, "polygon": [[128.6455078125, 409.53515625], [381.8205261230469, 409.53515625], [381.8205261230469, 423.9659729003906], [128.6455078125, 423.9659729003906]]}, {"title": "12.5 Lists and tuples", "heading_level": null, "page_id": 137, "polygon": [[85.763671875, 199.16015625], [229.5, 199.16015625], [229.5, 215.7159423828125], [85.763671875, 215.7159423828125]]}, {"title": "12.6 Dictionaries and tuples", "heading_level": null, "page_id": 138, "polygon": [[128.57080078125, 185.9150390625], [322.0256042480469, 185.9150390625], [322.0256042480469, 200.4730224609375], [128.57080078125, 200.4730224609375]]}, {"title": "12.7 Comparing tuples", "heading_level": null, "page_id": 139, "polygon": [[85.53955078125, 525.55078125], [244.19384765625, 525.55078125], [244.19384765625, 540.5119476318359], [85.53955078125, 540.5119476318359]]}, {"title": "12.8 Sequences of sequences", "heading_level": null, "page_id": 140, "polygon": [[127.97314453125, 488.0967102050781], [326.0281982421875, 488.0967102050781], [326.0281982421875, 502.44293212890625], [127.97314453125, 502.44293212890625]]}, {"title": "12.9 Debugging", "heading_level": null, "page_id": 141, "polygon": [[85.6142578125, 222.5216064453125], [201.2607421875, 222.5216064453125], [201.2607421875, 237.251953125], [85.6142578125, 237.251953125]]}, {"title": "12.10 Glossary", "heading_level": null, "page_id": 142, "polygon": [[129.60000610351562, 85.95379638671875], [234.3990020751953, 85.95379638671875], [234.3990020751953, 100.29998779296875], [129.60000610351562, 100.29998779296875]]}, {"title": "12.11 Exercises", "heading_level": null, "page_id": 142, "polygon": [[128.72021484375, 343.4568786621094], [235.97714233398438, 343.4568786621094], [235.97714233398438, 357.8031005859375], [128.72021484375, 357.8031005859375]]}, {"title": "Chapter 13", "heading_level": null, "page_id": 144, "polygon": [[129.01904296875, 166.9874267578125], [231.591796875, 166.9874267578125], [231.591796875, 187.64990234375], [129.01904296875, 187.64990234375]]}, {"title": "Case study: data structure\nselection", "heading_level": null, "page_id": 144, "polygon": [[128.6455078125, 225.209228515625], [423.140625, 225.209228515625], [423.140625, 280.4812927246094], [128.6455078125, 280.4812927246094]]}, {"title": "13.1 Word frequency analysis", "heading_level": null, "page_id": 144, "polygon": [[128.86962890625, 331.611328125], [331.3649597167969, 331.611328125], [331.3649597167969, 346.58087158203125], [128.86962890625, 346.58087158203125]]}, {"title": "13.2 Random numbers", "heading_level": null, "page_id": 145, "polygon": [[85.39013671875, 85.95379638671875], [243.544921875, 85.95379638671875], [243.544921875, 100.546875], [85.39013671875, 100.546875]]}, {"title": "13.3 Word histogram", "heading_level": null, "page_id": 146, "polygon": [[127.8984375, 85.95379638671875], [275.220703125, 85.95379638671875], [275.220703125, 100.29998779296875], [127.8984375, 100.29998779296875]]}, {"title": "13.4 Most common words", "heading_level": null, "page_id": 147, "polygon": [[85.53955078125, 154.7841796875], [263.7333679199219, 154.7841796875], [263.7333679199219, 169.3389892578125], [85.53955078125, 169.3389892578125]]}, {"title": "13.5 Optional parameters", "heading_level": null, "page_id": 147, "polygon": [[85.3154296875, 540.6328125], [260.9214782714844, 540.6328125], [260.9214782714844, 555.4039306640625], [85.3154296875, 555.4039306640625]]}, {"title": "13.6 Dictionary subtraction", "heading_level": null, "page_id": 148, "polygon": [[127.*********75, 209.6015625], [316.8609924316406, 209.6015625], [316.8609924316406, 224.30792*********], [127.*********75, 224.30792*********]]}, {"title": "13.7 Random words", "heading_level": null, "page_id": 148, "polygon": [[127.7490234375, 641.8667907714844], [269.3935546875, 641.8667907714844], [269.3935546875, 656.2129821777344], [127.7490234375, 656.2129821777344]]}, {"title": "13.8 <PERSON><PERSON> analysis", "heading_level": null, "page_id": 149, "polygon": [[85.3154296875, 417.85174560546875], [234.6392822265625, 417.85174560546875], [234.6392822265625, 432.1979675292969], [85.3154296875, 432.1979675292969]]}, {"title": "13.9 Data structures", "heading_level": null, "page_id": 150, "polygon": [[128.3466796875, 616.8848419189453], [268.24176025390625, 616.8848419189453], [268.24176025390625, 631.2310333251953], [128.3466796875, 631.2310333251953]]}, {"title": "13.10 Debugging", "heading_level": null, "page_id": 152, "polygon": [[129.60000610351562, 85.95379638671875], [250.41796875, 85.95379638671875], [250.41796875, 100.4501953125], [129.60000610351562, 100.4501953125]]}, {"title": "13.11 Glossary", "heading_level": null, "page_id": 153, "polygon": [[85.46484375, 85.95379638671875], [191.25, 85.95379638671875], [191.25, 100.29998779296875], [85.46484375, 100.29998779296875]]}, {"title": "13.12 Exercises", "heading_level": null, "page_id": 153, "polygon": [[85.53955078125, 265.482421875], [192.77706909179688, 265.482421875], [192.77706909179688, 280.2669677734375], [85.53955078125, 280.2669677734375]]}, {"title": "Chapter 14", "heading_level": null, "page_id": 154, "polygon": [[128.*********, 162.615234375], [232.787109375, 162.615234375], [232.787109375, 184.32196044921875], [128.*********, 184.32196044921875]]}, {"title": "Files", "heading_level": null, "page_id": 154, "polygon": [[127.8984375, 217.916015625], [184.974609375, 217.916015625], [184.974609375, 243.34136962890625], [127.8984375, 243.34136962890625]]}, {"title": "14.1 Persistence", "heading_level": null, "page_id": 154, "polygon": [[128.6455078125, 291.7667541503906], [242.4990234375, 291.7667541503906], [242.4990234375, 306.11297607421875], [128.6455078125, 306.11297607421875]]}, {"title": "14.2 Reading and writing", "heading_level": null, "page_id": 154, "polygon": [[127.8984375, 544.88671875], [305.701171875, 544.88671875], [305.701171875, 559.58203125], [127.8984375, 559.58203125]]}, {"title": "14.3 Format operator", "heading_level": null, "page_id": 155, "polygon": [[85.763671875, 227.187744140625], [232.189453125, 227.187744140625], [232.189453125, 241.533935546875], [85.763671875, 241.533935546875]]}, {"title": "14.4 Filenames and paths", "heading_level": null, "page_id": 156, "polygon": [[127.*********75, 172.94970703125], [303.7198791503906, 172.94970703125], [303.7198791503906, 187.2958984375], [127.*********75, 187.2958984375]]}, {"title": "14.5 Catching exceptions", "heading_level": null, "page_id": 157, "polygon": [[85.3154296875, 233.19140625], [258.0380859375, 233.19140625], [258.0380859375, 249.285888671875], [85.3154296875, 249.285888671875]]}, {"title": "14.6 Databases", "heading_level": null, "page_id": 158, "polygon": [[128.197265625, 155.07421875], [235.97708129882812, 155.07421875], [235.97708129882812, 169.46295*********], [128.197265625, 169.46295*********]]}, {"title": "14.7 Pickling", "heading_level": null, "page_id": 158, "polygon": [[128.0478515625, 584.33203125], [224.02682495117188, 584.33203125], [224.02682495117188, 598.7357788085938], [128.0478515625, 598.7357788085938]]}, {"title": "14.8 Pipes", "heading_level": null, "page_id": 159, "polygon": [[85.68896484375, 460.58203125], [162.263671875, 460.58203125], [162.263671875, 475.7430114746094], [85.68896484375, 475.7430114746094]]}, {"title": "14.9 Writing modules", "heading_level": null, "page_id": 160, "polygon": [[127.30078125, 564.4838562011719], [279.703125, 564.4838562011719], [279.703125, 578.8300476074219], [127.30078125, 578.8300476074219]]}, {"title": "14.10 Debugging", "heading_level": null, "page_id": 161, "polygon": [[85.6142578125, 524.77734375], [207.1519*********, 524.77734375], [207.1519*********, 539.7729644775391], [85.6142578125, 539.7729644775391]]}, {"title": "14.11 Glossary", "heading_level": null, "page_id": 162, "polygon": [[128.*********, 224.9427490234375], [234.87890625, 224.9427490234375], [234.87890625, 239.2889404296875], [128.*********, 239.2889404296875]]}, {"title": "14.12 Exercises", "heading_level": null, "page_id": 162, "polygon": [[128.3466796875, 548.1956634521484], [235.97705078125, 548.1956634521484], [235.97705078125, 562.5418548583984], [128.3466796875, 562.5418548583984]]}, {"title": "Chapter 15", "heading_level": null, "page_id": 164, "polygon": [[128.6455078125, 163.001953125], [232.6376953125, 163.001953125], [232.6376953125, 184.200927734375], [128.6455078125, 184.200927734375]]}, {"title": "Classes and objects", "heading_level": null, "page_id": 164, "polygon": [[128.57080078125, 218.109375], [348.4700927734375, 218.109375], [348.4700927734375, 243.09832763671875], [128.57080078125, 243.09832763671875]]}, {"title": "15.1 User-defined types", "heading_level": null, "page_id": 164, "polygon": [[128.3466796875, 353.8217468261719], [292.1044921875, 353.8217468261719], [292.1044921875, 368.16796875], [128.3466796875, 368.16796875]]}, {"title": "15.2 Attributes", "heading_level": null, "page_id": 165, "polygon": [[85.763671875, 366.416015625], [192.77703857421875, 366.416015625], [192.77703857421875, 381.3328857421875], [85.763671875, 381.3328857421875]]}, {"title": "15.3 Rectangles", "heading_level": null, "page_id": 166, "polygon": [[128.0478515625, 317.8707580566406], [240.1*********, 317.8707580566406], [240.1*********, 332.21697998046875], [128.0478515625, 332.21697998046875]]}, {"title": "15.4 Instances as return values", "heading_level": null, "page_id": 167, "polygon": [[85.9130859375, 317.4716491699219], [294.3768615722656, 317.4716491699219], [294.3768615722656, 331.81787109375], [85.9130859375, 331.81787109375]]}, {"title": "15.5 Objects are mutable", "heading_level": null, "page_id": 167, "polygon": [[85.98779296875, 530.023681640625], [257.888671875, 530.023681640625], [257.888671875, 544.3698883056641], [85.98779296875, 544.3698883056641]]}, {"title": "15.6 <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 168, "polygon": [[127.7490234375, 302.9747009277344], [225.7646484375, 302.9747009277344], [225.7646484375, 317.3209228515625], [127.7490234375, 317.3209228515625]]}, {"title": "15.7 Debugging", "heading_level": null, "page_id": 169, "polygon": [[85.763671875, 493.453125], [199.9788055419922, 493.453125], [199.9788055419922, 508.17291259765625], [85.763671875, 508.17291259765625]]}, {"title": "15.8 Glossary", "heading_level": null, "page_id": 170, "polygon": [[127.4501953125, 138.96173*********], [227.2587890625, 138.96173*********], [227.2587890625, 153.30792*********], [127.4501953125, 153.30792*********]]}, {"title": "15.9 Exercises", "heading_level": null, "page_id": 170, "polygon": [[128.49609375, 395.608642578125], [228.80392456054688, 395.608642578125], [228.80392456054688, 409.9548645019531], [128.49609375, 409.9548645019531]]}, {"title": "Chapter 16", "heading_level": null, "page_id": 172, "polygon": [[128.*********, 162.7974853515625], [232.787109375, 162.7974853515625], [232.787109375, 183.4599609375], [128.*********, 183.4599609375]]}, {"title": "Classes and functions", "heading_level": null, "page_id": 172, "polygon": [[128.86962890625, 216.830322265625], [376.008544921875, 216.830322265625], [376.008544921875, 241.61737060546875], [128.86962890625, 241.61737060546875]]}, {"title": "16.1 Time", "heading_level": null, "page_id": 172, "polygon": [[128.86962890625, 336.0127868652344], [202.53607177734375, 336.0127868652344], [202.53607177734375, 350.3590087890625], [128.86962890625, 350.3590087890625]]}, {"title": "16.2 Pure functions", "heading_level": null, "page_id": 172, "polygon": [[127.8984375, 628.921875], [265.0711975097656, 628.921875], [265.0711975097656, 643.26806640625], [127.8984375, 643.26806640625]]}, {"title": "16.3 Modifiers", "heading_level": null, "page_id": 174, "polygon": [[128.3466796875, 262.001953125], [232.80654907226562, 262.001953125], [232.80654907226562, 276.3709716796875], [128.3466796875, 276.3709716796875]]}, {"title": "16.4 Prototyping versus planning", "heading_level": null, "page_id": 175, "polygon": [[84.94189453125, 85.41650390625], [314.666015625, 85.41650390625], [314.666015625, 100.546875], [84.94189453125, 100.546875]]}, {"title": "16.5 Debugging", "heading_level": null, "page_id": 176, "polygon": [[127.7490234375, 183.3677978515625], [243.3955078125, 183.3677978515625], [243.3955078125, 197.7139892578125], [127.7490234375, 197.7139892578125]]}, {"title": "16.6 Glossary", "heading_level": null, "page_id": 176, "polygon": [[127.8984375, 624.05078125], [227.22596740722656, 624.05078125], [227.22596740722656, 638.39697265625], [127.8984375, 638.39697265625]]}, {"title": "16.7 Exercises", "heading_level": null, "page_id": 177, "polygon": [[85.68896484375, 225.21575927734375], [185.60400390625, 225.21575927734375], [185.60400390625, 239.56195068359375], [85.68896484375, 239.56195068359375]]}, {"title": "Chapter 17", "heading_level": null, "page_id": 178, "polygon": [[128.27197265625, 161.26171875], [232.787109375, 161.26171875], [232.787109375, 183.00201416015625], [128.27197265625, 183.00201416015625]]}, {"title": "Classes and methods", "heading_level": null, "page_id": 178, "polygon": [[129.16845703125, 215.208984375], [366.662109375, 215.208984375], [366.662109375, 240.700439453125], [129.16845703125, 240.700439453125]]}, {"title": "17.1 Object-oriented features", "heading_level": null, "page_id": 178, "polygon": [[128.6455078125, 333.73828125], [329.30859375, 333.73828125], [329.30859375, 348.3430480957031], [128.6455078125, 348.3430480957031]]}, {"title": "17.2 Printing objects", "heading_level": null, "page_id": 179, "polygon": [[85.46484375, 216.17578125], [229.83331298828125, 216.17578125], [229.83331298828125, 231.40789*********], [85.46484375, 231.40789*********]]}, {"title": "17.3 Another example", "heading_level": null, "page_id": 180, "polygon": [[127.52490234375, 365.8359375], [282.09375, 365.8359375], [282.09375, 380.197998046875], [127.52490234375, 380.197998046875]]}, {"title": "17.4 A more complicated example", "heading_level": null, "page_id": 181, "polygon": [[85.3154296875, 85.70654296875], [317.35546875, 85.70654296875], [317.35546875, 100.29998779296875], [85.3154296875, 100.29998779296875]]}, {"title": "17.5 The init method", "heading_level": null, "page_id": 181, "polygon": [[85.763671875, 278.4375], [231.84181213378906, 278.4375], [231.84181213378906, 293.90625], [85.763671875, 293.90625]]}, {"title": "17.6 The __str__ method", "heading_level": null, "page_id": 182, "polygon": [[128.6455078125, 83.87713623046875], [302.9117126464844, 83.87713623046875], [302.9117126464844, 100.29998779296875], [128.6455078125, 100.29998779296875]]}, {"title": "17.7 Operator overloading", "heading_level": null, "page_id": 182, "polygon": [[127.8984375, 363.1026611328125], [311.080078125, 363.1026611328125], [311.080078125, 377.4488830566406], [127.8984375, 377.4488830566406]]}, {"title": "17.8 Type-based dispatch", "heading_level": null, "page_id": 183, "polygon": [[85.6142578125, 85.95379638671875], [261.3251953125, 85.95379638671875], [261.3251953125, 100.29998779296875], [85.6142578125, 100.29998779296875]]}, {"title": "17.9 Polymorphism", "heading_level": null, "page_id": 184, "polygon": [[128.42138671875, 223.18475341796875], [266.29058837890625, 223.18475341796875], [266.29058837890625, 237.53094482421875], [128.42138671875, 237.53094482421875]]}, {"title": "17.10 Debugging", "heading_level": null, "page_id": 185, "polygon": [[85.3154296875, 85.12646484375], [208.1337890625, 85.12646484375], [208.1337890625, 101.513671875], [85.3154296875, 101.513671875]]}, {"title": "17.11 Interface and implementation", "heading_level": null, "page_id": 185, "polygon": [[85.3154296875, 400.640625], [328.26263427734375, 400.640625], [328.26263427734375, 417.40594482421875], [85.3154296875, 417.40594482421875]]}, {"title": "17.12 Glossary", "heading_level": null, "page_id": 186, "polygon": [[128.3466796875, 239.185791015625], [234.7294921875, 239.185791015625], [234.7294921875, 253.531982421875], [128.3466796875, 253.531982421875]]}, {"title": "17.13 Exercises", "heading_level": null, "page_id": 186, "polygon": [[128.6455078125, 522.84375], [236.2*********, 522.84375], [236.2*********, 537.*************], [128.6455078125, 537.*************]]}, {"title": "Chapter 18", "heading_level": null, "page_id": 188, "polygon": [[129.09375, 163.1953125], [233.68359375, 163.1953125], [233.68359375, 184.29296875], [129.09375, 184.29296875]]}, {"title": "Inheritance", "heading_level": null, "page_id": 188, "polygon": [[128.9443359375, 218.302734375], [259.8310546875, 218.302734375], [259.8310546875, 243.2823486328125], [128.9443359375, 243.2823486328125]]}, {"title": "18.1 Card objects", "heading_level": null, "page_id": 188, "polygon": [[128.6455078125, 395.9047546386719], [250.8662109375, 395.9047546386719], [250.8662109375, 410.30859375], [128.6455078125, 410.30859375]]}, {"title": "18.2 Class attributes", "heading_level": null, "page_id": 189, "polygon": [[85.9130859375, 382.46484375], [227.**************, 382.46484375], [227.**************, 396.8459167480469], [85.9130859375, 396.8459167480469]]}, {"title": "18.3 Comparing cards", "heading_level": null, "page_id": 190, "polygon": [[128.3466796875, 483.01171875], [281.0240783691406, 483.01171875], [281.0240783691406, 497.5999755859375], [128.3466796875, 497.5999755859375]]}, {"title": "18.4 Decks", "heading_level": null, "page_id": 191, "polygon": [[85.763671875, 477.44677734375], [166.**************, 477.44677734375], [166.**************, 491.7929992675781], [85.763671875, 491.7929992675781]]}, {"title": "18.5 Printing the deck", "heading_level": null, "page_id": 192, "polygon": [[127.8984375, 85.95379638671875], [283.587890625, 85.95379638671875], [283.587890625, 100.29998779296875], [127.8984375, 100.29998779296875]]}, {"title": "18.6 Add, remove, shuffle and sort", "heading_level": null, "page_id": 192, "polygon": [[128.49609375, 456.9557800292969], [362.*************, 456.9557800292969], [362.*************, 471.302001953125], [128.49609375, 471.302001953125]]}, {"title": "18.7 Inheritance", "heading_level": null, "page_id": 193, "polygon": [[85.763671875, 266.0625], [200.7535858154297, 266.0625], [200.7535858154297, 280.8150634765625], [85.763671875, 280.8150634765625]]}, {"title": "18.8 Class diagrams", "heading_level": null, "page_id": 194, "polygon": [[127.4501953125, 596.4488372802734], [268.25604248046875, 596.4488372802734], [268.25604248046875, 610.7950286865234], [127.4501953125, 610.7950286865234]]}, {"title": "18.9 Debugging", "heading_level": null, "page_id": 195, "polygon": [[85.6142578125, 563.0625], [202.1572265625, 563.0625], [202.1572265625, 578.53125], [85.6142578125, 578.53125]]}, {"title": "18.10 Data encapsulation", "heading_level": null, "page_id": 196, "polygon": [[128.86962890625, 423.11175537109375], [302.5148010253906, 423.11175537109375], [302.5148010253906, 437.4579772949219], [128.86962890625, 437.4579772949219]]}, {"title": "18.11 Glossary", "heading_level": null, "page_id": 197, "polygon": [[85.39013671875, 538.69921875], [191.19908142089844, 538.69921875], [191.19908142089844, 553.2739562988281], [85.39013671875, 553.2739562988281]]}, {"title": "18.12 Exercises", "heading_level": null, "page_id": 198, "polygon": [[128.9443359375, 277.52880859375], [235.97714233398438, 277.52880859375], [235.97714233398438, 291.875], [128.9443359375, 291.875]]}, {"title": "Chapter 19", "heading_level": null, "page_id": 200, "polygon": [[128.9443359375, 163.58203125], [232.0400390625, 163.58203125], [232.0400390625, 185.2369384765625], [128.9443359375, 185.2369384765625]]}, {"title": "Case study: <PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 200, "polygon": [[128.27197265625, 220.*********], [352.318359375, 220.*********], [352.318359375, 245.17132568359375], [128.27197265625, 245.17132568359375]]}, {"title": "19.1 GUI", "heading_level": null, "page_id": 200, "polygon": [[128.*********, 294.5127258300781], [198.421875, 294.5127258300781], [198.421875, 308.85894*********], [128.*********, 308.85894*********]]}, {"title": "19.2 Buttons and callbacks", "heading_level": null, "page_id": 201, "polygon": [[85.68896484375, 316.916015625], [269.2710266113281, 316.916015625], [269.2710266113281, 331.5769348144531], [85.68896484375, 331.5769348144531]]}, {"title": "19.3 Canvas widgets", "heading_level": null, "page_id": 202, "polygon": [[128.6455078125, 335.95*********], [272.3818359375, 335.95*********], [272.3818359375, 350.2978820800781], [128.6455078125, 350.2978820800781]]}, {"title": "19.4 Coordinate sequences", "heading_level": null, "page_id": 203, "polygon": [[85.53955078125, 171.7998046875], [269.6870422363281, 171.7998046875], [269.6870422363281, 186.14697265625], [85.53955078125, 186.14697265625]]}, {"title": "19.5 More widgets", "heading_level": null, "page_id": 203, "polygon": [[85.6142578125, 465.609375], [216.30496215820312, 465.609375], [216.30496215820312, 480.4320373535156], [85.6142578125, 480.4320373535156]]}, {"title": "19.6 Packing widgets", "heading_level": null, "page_id": 204, "polygon": [[128.3466796875, 402.5648498535156], [277.02154541015625, 402.5648498535156], [277.02154541015625, 416.91107177734375], [128.3466796875, 416.91107177734375]]}, {"title": "19.7 Menus and Callables", "heading_level": null, "page_id": 206, "polygon": [[128.27197265625, 565.2227935791016], [308.091796875, 565.2227935791016], [308.091796875, 579.5689849853516], [128.27197265625, 579.5689849853516]]}, {"title": "19.8 Binding", "heading_level": null, "page_id": 207, "polygon": [[85.6142578125, 404.94476318359375], [179.7451171875, 404.94476318359375], [179.7451171875, 419.2909851074219], [85.6142578125, 419.2909851074219]]}, {"title": "19.9 Debugging", "heading_level": null, "page_id": 209, "polygon": [[85.6142578125, 400.25390625], [200.6630859375, 400.25390625], [200.6630859375, 414.94921875], [85.6142578125, 414.94921875]]}, {"title": "19.10 Glossary", "heading_level": null, "page_id": 210, "polygon": [[128.6455078125, 342.93377*********], [234.7294921875, 342.93377*********], [234.7294921875, 357.2799987792969], [128.6455078125, 357.2799987792969]]}, {"title": "19.11 Exercises", "heading_level": null, "page_id": 211, "polygon": [[85.*********, 84.4013671875], [194.0888671875, 84.4013671875], [194.0888671875, 99.97796630859375], [85.*********, 99.97796630859375]]}, {"title": "Appendix A", "heading_level": null, "page_id": 214, "polygon": [[128.6455078125, 163.*********], [246.3837890625, 163.*********], [246.3837890625, 185.823974609375], [128.6455078125, 185.823974609375]]}, {"title": "Debugging", "heading_level": null, "page_id": 214, "polygon": [[128.*********, 220.*********], [259.*********, 220.*********], [259.*********, 246.34539*********], [128.*********, 246.34539*********]]}, {"title": "A.1 Syntax errors", "heading_level": null, "page_id": 214, "polygon": [[129.*********, 568.3808135986328], [252.34609985351562, 568.3808135986328], [252.34609985351562, 582.7270050048828], [129.*********, 582.7270050048828]]}, {"title": "A.1.1 I keep making changes and it makes no difference.", "heading_level": null, "page_id": 215, "polygon": [[85.9130859375, 498.48046875], [403.41796875, 498.48046875], [403.41796875, 511.0899658203125], [85.9130859375, 511.0899658203125]]}, {"title": "A.2 Runtime errors", "heading_level": null, "page_id": 216, "polygon": [[128.27197265625, 233.96484375], [265.0855407714844, 233.96484375], [265.0855407714844, 248.33489990234375], [128.27197265625, 248.33489990234375]]}, {"title": "A.2.1 My program does absolutely nothing.", "heading_level": null, "page_id": 216, "polygon": [[128.3466796875, 308.*********], [374.3588562011719, 308.*********], [374.3588562011719, 321.02691650390625], [128.3466796875, 321.02691650390625]]}, {"title": "A.2.2 My program hangs.", "heading_level": null, "page_id": 216, "polygon": [[129.01904296875, 424.6171875], [276.1171875, 424.6171875], [276.1171875, 437.11590576171875], [129.01904296875, 437.11590576171875]]}, {"title": "Infinite Loop", "heading_level": null, "page_id": 217, "polygon": [[85.*********, 87.35009765625], [147.919921875, 87.35009765625], [147.919921875, 98.70074462890625], [85.*********, 98.70074462890625]]}, {"title": "Infinite Recursion", "heading_level": null, "page_id": 217, "polygon": [[85.763671875, 328.904296875], [169.2861328125, 328.904296875], [169.2861328125, 340.1767883300781], [85.763671875, 340.1767883300781]]}, {"title": "Flow of Execution", "heading_level": null, "page_id": 217, "polygon": [[85.763671875, 521.68359375], [168.3896484375, 521.68359375], [168.3896484375, 533.2448425292969], [85.763671875, 533.2448425292969]]}, {"title": "A.2.3 When I run the program I get an exception.", "heading_level": null, "page_id": 217, "polygon": [[85.53955078125, 623.9998321533203], [359.7890625, 623.9998321533203], [359.7890625, 635.9550323486328], [85.53955078125, 635.9550323486328]]}, {"title": "A.2.4 I added so many print statements I get inundated with output.", "heading_level": null, "page_id": 218, "polygon": [[128.0478515625, 609.0523681640625], [511.3203430175781, 609.0523681640625], [511.3203430175781, 621.1488494873047], [128.0478515625, 621.1488494873047]]}, {"title": "A.3 Semantic errors", "heading_level": null, "page_id": 219, "polygon": [[86.2119140625, 290.8125], [225.7646484375, 290.8125], [225.7646484375, 306.06982421875], [86.2119140625, 306.06982421875]]}, {"title": "A.3.1 My program doesn't work.", "heading_level": null, "page_id": 219, "polygon": [[85.68896484375, 472.18359375], [270.8448791503906, 472.18359375], [270.8448791503906, 485.61285400390625], [85.68896484375, 485.61285400390625]]}, {"title": "A.3.2 I've got a big hairy expression and it doesn't do what I expect.", "heading_level": null, "page_id": 220, "polygon": [[129.*********, 190.3623046875], [504.9574279785156, 190.3623046875], [504.9574279785156, 202.9779052734375], [129.*********, 202.9779052734375]]}, {"title": "A.3.3 I've got a function or method that doesn't return what I expect.", "heading_level": null, "page_id": 220, "polygon": [[128.197265625, 571.5703125], [509.2732238769531, 571.5703125], [509.2732238769531, 583.7809906005859], [128.197265625, 583.7809906005859]]}, {"title": "A.3.4 I'm really, really stuck and I need help.", "heading_level": null, "page_id": 221, "polygon": [[84.7177734375, 85.80322265625], [339.46875, 85.80322265625], [339.46875, 99.24493408203125], [84.7177734375, 99.24493408203125]]}, {"title": "A.3.5 No, I really need help.", "heading_level": null, "page_id": 221, "polygon": [[85.9130859375, 351.333984375], [249.0732421875, 351.333984375], [249.0732421875, 364.482421875], [85.9130859375, 364.482421875]]}, {"title": "Appendix B", "heading_level": null, "page_id": 222, "polygon": [[128.12255859375, 163.388671875], [243.544921875, 163.388671875], [243.544921875, 184.8819580078125], [128.12255859375, 184.8819580078125]]}, {"title": "Analysis of Algorithms", "heading_level": null, "page_id": 222, "polygon": [[127.97314453125, 219.076171875], [393.9792175292969, 219.076171875], [393.9792175292969, 244.46136474609375], [127.97314453125, 244.46136474609375]]}, {"title": "B.1 Order of growth", "heading_level": null, "page_id": 223, "polygon": [[85.09130859375, 281.14453125], [228.603515625, 281.14453125], [228.603515625, 296.4639892578125], [85.09130859375, 296.4639892578125]]}, {"title": "B.2 Analysis of basic Python operations", "heading_level": null, "page_id": 225, "polygon": [[84.64306640625, 85.271484375], [357.697265625, 85.271484375], [357.697265625, 100.29998779296875], [84.64306640625, 100.29998779296875]]}, {"title": "B.3 Analysis of search algorithms", "heading_level": null, "page_id": 226, "polygon": [[129.01904296875, 453.54278564453125], [358.3215026855469, 453.54278564453125], [358.3215026855469, 467.8890075683594], [129.01904296875, 467.8890075683594]]}, {"title": "B.4 Hashtables", "heading_level": null, "page_id": 227, "polygon": [[85.6142578125, 248.466796875], [194.537109375, 248.466796875], [194.537109375, 263.1820068359375], [85.6142578125, 263.1820068359375]]}, {"title": "Appendix C", "heading_level": null, "page_id": 232, "polygon": [[128.49609375, 161.068359375], [246.533203125, 161.068359375], [246.533203125, 182.990966796875], [128.49609375, 182.990966796875]]}, {"title": "Lumpy", "heading_level": null, "page_id": 232, "polygon": [[129.09375, 215.89129638671875], [212.765625, 215.89129638671875], [212.765625, 241.69921875], [129.09375, 241.69921875]]}, {"title": "C.1 State diagram", "heading_level": null, "page_id": 232, "polygon": [[127.30078125, 665.9296875], [256.39453125, 665.9296875], [256.39453125, 680.3199615478516], [127.30078125, 680.3199615478516]]}, {"title": "C.2 <PERSON>ack diagram", "heading_level": null, "page_id": 233, "polygon": [[86.0625, 649.6875], [215.5015106201172, 649.6875], [215.5015106201172, 665.2679290771484], [86.0625, 665.2679290771484]]}, {"title": "C.3 Object diagrams", "heading_level": null, "page_id": 234, "polygon": [[127.8984375, 575.82421875], [273.12890625, 575.82421875], [273.12890625, 590.51953125], [127.8984375, 590.51953125]]}, {"title": "C.4 Function and class objects", "heading_level": null, "page_id": 236, "polygon": [[128.42138671875, 636.92578125], [335.9700012207031, 636.92578125], [335.9700012207031, 651.62109375], [128.42138671875, 651.62109375]]}, {"title": "C.5 Class Diagrams", "heading_level": null, "page_id": 237, "polygon": [[85.9130859375, 649.30078125], [225.1669921875, 649.30078125], [225.1669921875, 665.6630401611328], [85.9130859375, 665.6630401611328]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 7], ["Line", 3], ["SectionHeader", 2], ["Text", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7791}}, {"page_id": 1, "text_extraction_method": "surya", "block_counts": [["Text", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2122}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 12], ["Line", 6], ["Text", 4], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 42], ["Line", 11], ["Text", 5]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 31], ["Text", 8], ["ListItem", 4], ["SectionHeader", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 72], ["Line", 34], ["Text", 10], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 39], ["ListItem", 20], ["Text", 3], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 39], ["ListItem", 25], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4024}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 35], ["ListItem", 28], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 35], ["ListItem", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 34], ["ListItem", 29], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4048}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["<PERSON><PERSON><PERSON><PERSON>", 2], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 156], ["Span", 71], ["Line", 24], ["SectionHeader", 1], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2891}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 124], ["Span", 96], ["Line", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2353}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 112], ["Span", 84], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2865}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 253], ["Span", 116], ["Line", 33], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7349}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["TableCell", 84], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 3874}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["TableCell", 93], ["Line", 31], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4431}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 165], ["Span", 81], ["Line", 27], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8554}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 159], ["Span", 87], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8979}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 112], ["Span", 85], ["Line", 28], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TableOfContents", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 5907}}, {"page_id": 21, "text_extraction_method": "surya", "block_counts": [["<PERSON><PERSON><PERSON><PERSON>", 2], ["Line", 2], ["Span", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 58], ["Line", 29], ["Text", 6], ["SectionHeader", 3], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 41], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1760}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 36], ["Text", 13], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 39], ["Text", 10], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 41], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4050}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 37], ["Text", 9], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 38], ["Text", 9], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3846}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 32], ["ListItem", 19], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3846}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 76], ["Line", 18], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 28], ["Text", 7], ["SectionHeader", 3], ["Code", 3], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 118], ["Line", 39], ["Text", 9], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Code", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4159}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["TableCell", 71], ["Line", 42], ["Text", 13], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["Table", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1750}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 39], ["Text", 14], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Code", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 38], ["Text", 5], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 40], ["Text", 12], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3838}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 38], ["Text", 13], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Code", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 11442}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 33], ["ListItem", 7], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 78], ["Line", 27], ["Text", 7], ["SectionHeader", 4], ["Reference", 3], ["Code", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 42], ["Text", 8], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 100], ["Line", 40], ["Text", 12], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3073}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 42], ["Code", 7], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 39], ["Text", 14], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 45], ["Text", 7], ["Code", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 47], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Code", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 39], ["Text", 11], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 40], ["Text", 10], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1747}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 35], ["ListItem", 17], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 1], ["Caption", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3840}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 36], ["ListItem", 8], ["Text", 7], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3792}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 78], ["Line", 23], ["Text", 5], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4044}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 75], ["Line", 25], ["Text", 9], ["SectionHeader", 3], ["Code", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 38], ["Text", 15], ["Code", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 172], ["Line", 41], ["Text", 11], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 39], ["Text", 10], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 42], ["Text", 10], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 43], ["Text", 6], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 99], ["Line", 35], ["Text", 7], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Code", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2259}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 38], ["Text", 7], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 23], ["Text", 8], ["ListItem", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4173}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 61], ["Line", 12], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 30], ["Text", 5], ["SectionHeader", 4], ["Code", 3], ["Reference", 3]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 698}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 41], ["TableCell", 25], ["Text", 13], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Code", 2], ["Reference", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9060}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 41], ["Text", 6], ["SectionHeader", 3], ["Code", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 43], ["Text", 11], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 39], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 40], ["Text", 6], ["Code", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5072}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 42], ["Text", 9], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 153], ["Line", 39], ["Text", 7], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3938}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 39], ["ListItem", 8], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7531}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 30], ["ListItem", 10], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Figure", 1], ["Caption", 1], ["Code", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7789}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 79], ["Line", 29], ["Text", 7], ["SectionHeader", 3], ["Code", 3], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 42], ["Text", 13], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7941}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 45], ["Text", 9], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["ListItem", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7820}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 38], ["Text", 8], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 76, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 42], ["Text", 11], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["SectionHeader", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 77, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 40], ["Text", 16], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3937}}, {"page_id": 78, "text_extraction_method": "pdftext", "block_counts": [["Span", 186], ["Line", 48], ["Text", 7], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["Code", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3978}}, {"page_id": 79, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 45], ["Text", 8], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 80, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 38], ["Text", 9], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Code", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 81, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 39], ["ListItem", 6], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2], ["SectionHeader", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 82, "text_extraction_method": "pdftext", "block_counts": [["Span", 296], ["Line", 45], ["Text", 7], ["Code", 4], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Equation", 1], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6226}}, {"page_id": 83, "text_extraction_method": "pdftext", "block_counts": [["Span", 22], ["Line", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 84, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 29], ["Text", 9], ["SectionHeader", 3], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3828}}, {"page_id": 85, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 37], ["Text", 10], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2], ["Code", 2], ["Figure", 1], ["Caption", 1], ["ListItem", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 86, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 40], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Code", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 87, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 42], ["Text", 9], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4325}}, {"page_id": 88, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 45], ["Text", 7], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4083}}, {"page_id": 89, "text_extraction_method": "pdftext", "block_counts": [["Span", 105], ["Line", 38], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 90, "text_extraction_method": "pdftext", "block_counts": [["Span", 168], ["Line", 51], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["SectionHeader", 1], ["Equation", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 91, "text_extraction_method": "pdftext", "block_counts": [["Span", 38], ["Line", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Text", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 92, "text_extraction_method": "pdftext", "block_counts": [["Span", 96], ["Line", 26], ["Text", 10], ["SectionHeader", 4], ["Reference", 3], ["Code", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 93, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 42], ["Text", 9], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2534}}, {"page_id": 94, "text_extraction_method": "pdftext", "block_counts": [["Span", 115], ["Line", 42], ["Code", 4], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 95, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 41], ["Text", 12], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 96, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 42], ["Text", 11], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 97, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 41], ["Text", 11], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 98, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 41], ["Text", 8], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Code", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3993}}, {"page_id": 99, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 39], ["Text", 8], ["Code", 4], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["Figure", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2075}}, {"page_id": 100, "text_extraction_method": "pdftext", "block_counts": [["Span", 119], ["Line", 38], ["Text", 11], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 101, "text_extraction_method": "pdftext", "block_counts": [["Span", 73], ["Line", 28], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 102, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 32], ["Text", 7], ["SectionHeader", 3], ["Code", 3], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 103, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 42], ["Text", 15], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 757}}, {"page_id": 104, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 43], ["Text", 10], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1805}}, {"page_id": 105, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 46], ["Text", 7], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 106, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 34], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["Code", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 107, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 38], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 108, "text_extraction_method": "pdftext", "block_counts": [["Span", 90], ["Line", 28], ["Text", 10], ["SectionHeader", 4], ["Reference", 3], ["Code", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 109, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 42], ["Text", 6], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 110, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 37], ["Text", 10], ["Code", 4], ["SectionHeader", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 738}}, {"page_id": 111, "text_extraction_method": "pdftext", "block_counts": [["Span", 208], ["Line", 40], ["Text", 8], ["Code", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 11464}}, {"page_id": 112, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 44], ["Text", 12], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 113, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 45], ["Text", 12], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5073}}, {"page_id": 114, "text_extraction_method": "pdftext", "block_counts": [["Span", 184], ["Line", 39], ["Text", 10], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4054}}, {"page_id": 115, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 44], ["Text", 11], ["TableCell", 8], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["Figure", 1], ["Table", 1], ["Caption", 1], ["TextInlineMath", 1], ["SectionHeader", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1599}}, {"page_id": 116, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 45], ["Text", 8], ["TextInlineMath", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Caption", 2], ["Code", 2], ["Equation", 1], ["Figure", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 117, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 40], ["Text", 9], ["Code", 3], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListItem", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 118, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 38], ["Text", 11], ["TableCell", 8], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Table", 1], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3321}}, {"page_id": 119, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 39], ["Text", 11], ["ListItem", 6], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 120, "text_extraction_method": "pdftext", "block_counts": [["Span", 69], ["Line", 20], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 121, "text_extraction_method": "pdftext", "block_counts": [["<PERSON><PERSON><PERSON><PERSON>", 2], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 122, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 32], ["Text", 10], ["Code", 5], ["SectionHeader", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 8052}}, {"page_id": 123, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 41], ["Text", 9], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Reference", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 124, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 45], ["Text", 10], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 125, "text_extraction_method": "pdftext", "block_counts": [["Span", 132], ["Line", 41], ["Text", 11], ["Code", 3], ["TextInlineMath", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4856}}, {"page_id": 126, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 43], ["Text", 9], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 127, "text_extraction_method": "pdftext", "block_counts": [["Span", 166], ["Line", 56], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Code", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 128, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 48], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["Code", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 129, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 41], ["Text", 10], ["Code", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 130, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 38], ["Text", 10], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 131, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 36], ["Text", 14], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3827}}, {"page_id": 132, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 41], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Reference", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 133, "text_extraction_method": "pdftext", "block_counts": [["<PERSON><PERSON><PERSON><PERSON>", 2], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 134, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 31], ["Text", 9], ["Code", 5], ["SectionHeader", 3], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4051}}, {"page_id": 135, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 39], ["Text", 10], ["Code", 4], ["TextInlineMath", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6291}}, {"page_id": 136, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 43], ["Text", 11], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3681}}, {"page_id": 137, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 42], ["Text", 9], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["TextInlineMath", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 6124}}, {"page_id": 138, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 41], ["Text", 13], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4148}}, {"page_id": 139, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 43], ["TableCell", 37], ["Text", 9], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 2], ["Caption", 2], ["Code", 2], ["TableGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 5526}}, {"page_id": 140, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 40], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Code", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 141, "text_extraction_method": "pdftext", "block_counts": [["Span", 131], ["Line", 45], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["Code", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 142, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 37], ["ListItem", 9], ["Text", 7], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 143, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 30], ["Text", 8], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 144, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 27], ["Text", 9], ["SectionHeader", 3], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 145, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 42], ["Text", 9], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2477}}, {"page_id": 146, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 40], ["Text", 12], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 147, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 41], ["Text", 9], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 741}}, {"page_id": 148, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 42], ["Text", 10], ["Code", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2529}}, {"page_id": 149, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 38], ["Text", 11], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Code", 1], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 150, "text_extraction_method": "pdftext", "block_counts": [["Span", 91], ["Line", 40], ["Text", 11], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 151, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 45], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Code", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 152, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 42], ["Text", 9], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 153, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 27], ["Text", 8], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1], ["Equation", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7672}}, {"page_id": 154, "text_extraction_method": "pdftext", "block_counts": [["Span", 76], ["Line", 27], ["Text", 9], ["SectionHeader", 4], ["Reference", 3], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 155, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 41], ["Text", 13], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4133}}, {"page_id": 156, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 40], ["Text", 13], ["Code", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 157, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 44], ["Text", 11], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 158, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 37], ["Text", 13], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2787}}, {"page_id": 159, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 45], ["Text", 10], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1], ["Footnote", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 160, "text_extraction_method": "pdftext", "block_counts": [["Span", 134], ["Line", 42], ["Text", 9], ["Code", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 161, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 40], ["Text", 12], ["Code", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 162, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 37], ["ListItem", 11], ["Text", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Code", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 163, "text_extraction_method": "pdftext", "block_counts": [["<PERSON><PERSON><PERSON><PERSON>", 2], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 164, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 26], ["Text", 9], ["SectionHeader", 3], ["ListItem", 3], ["Reference", 2], ["Code", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 165, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 43], ["Text", 12], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Caption", 1], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3947}}, {"page_id": 166, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 39], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["TextInlineMath", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 167, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 44], ["Text", 6], ["Code", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 168, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 42], ["Text", 7], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 169, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 53], ["Text", 8], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 170, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 38], ["Text", 14], ["ListItem", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Code", 2], ["TextInlineMath", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 13603}}, {"page_id": 171, "text_extraction_method": "pdftext", "block_counts": [["Span", 64], ["Line", 20], ["Text", 5], ["ListItem", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 172, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 28], ["Text", 8], ["Reference", 5], ["SectionHeader", 4], ["Code", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1742}}, {"page_id": 173, "text_extraction_method": "pdftext", "block_counts": [["Span", 123], ["Line", 46], ["Text", 6], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 174, "text_extraction_method": "pdftext", "block_counts": [["Span", 116], ["Line", 40], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 175, "text_extraction_method": "pdftext", "block_counts": [["Span", 142], ["Line", 45], ["Text", 12], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 176, "text_extraction_method": "pdftext", "block_counts": [["Span", 121], ["Line", 41], ["Text", 9], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 177, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 29], ["ListItem", 8], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 178, "text_extraction_method": "pdftext", "block_counts": [["Span", 85], ["Line", 29], ["Text", 8], ["SectionHeader", 3], ["ListItem", 2], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 179, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 42], ["Text", 9], ["Code", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 180, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 40], ["Text", 10], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["Reference", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 181, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 42], ["Text", 13], ["Code", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0}}, {"page_id": 182, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 36], ["Text", 11], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 183, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 43], ["Text", 8], ["Code", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3832}}, {"page_id": 184, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 42], ["Text", 7], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3848}}, {"page_id": 185, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 41], ["Text", 13], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Code", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4030}}, {"page_id": 186, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 36], ["ListItem", 11], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 2], ["Reference", 2], ["TextInlineMath", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 187, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 35], ["Text", 9], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 1], ["Caption", 1], ["Code", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 188, "text_extraction_method": "pdftext", "block_counts": [["Span", 98], ["Line", 29], ["TableCell", 24], ["Text", 7], ["SectionHeader", 3], ["Reference", 2], ["Table", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 830}}, {"page_id": 189, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 39], ["Text", 11], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 190, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 42], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["Code", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 191, "text_extraction_method": "pdftext", "block_counts": [["Span", 138], ["Line", 40], ["Text", 8], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 11008}}, {"page_id": 192, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 40], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Code", 2], ["Reference", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3916}}, {"page_id": 193, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 39], ["Text", 16], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 194, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 43], ["Text", 11], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 195, "text_extraction_method": "pdftext", "block_counts": [["Span", 126], ["Line", 35], ["Text", 10], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 196, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 41], ["Text", 12], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["TextInlineMath", 2], ["Code", 1], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 197, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 38], ["ListItem", 9], ["Text", 4], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 198, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 37], ["Text", 12], ["ListItem", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3828}}, {"page_id": 199, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 37], ["ListItem", 9], ["Text", 5], ["ListGroup", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 200, "text_extraction_method": "pdftext", "block_counts": [["Span", 77], ["Line", 26], ["Text", 12], ["SectionHeader", 3], ["Reference", 2], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 201, "text_extraction_method": "pdftext", "block_counts": [["Span", 111], ["Line", 36], ["Text", 19], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Code", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 202, "text_extraction_method": "pdftext", "block_counts": [["Span", 163], ["Line", 42], ["Text", 20], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Code", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3853}}, {"page_id": 203, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 36], ["Text", 18], ["Code", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 204, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Line", 42], ["Text", 14], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Reference", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 205, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 28], ["Text", 6], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 7820}}, {"page_id": 206, "text_extraction_method": "pdftext", "block_counts": [["Span", 139], ["Line", 40], ["Text", 8], ["Code", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 207, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 41], ["Text", 11], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 208, "text_extraction_method": "pdftext", "block_counts": [["Span", 143], ["Line", 44], ["Text", 9], ["Code", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3947}}, {"page_id": 209, "text_extraction_method": "pdftext", "block_counts": [["Span", 110], ["Line", 40], ["Text", 10], ["Code", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 210, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 37], ["ListItem", 17], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 211, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 42], ["Text", 10], ["Code", 3], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 212, "text_extraction_method": "pdftext", "block_counts": [["Span", 63], ["Line", 17], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 213, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 214, "text_extraction_method": "pdftext", "block_counts": [["Span", 63], ["Line", 27], ["Text", 4], ["SectionHeader", 3], ["ListItem", 3], ["Reference", 2], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 215, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 37], ["ListItem", 10], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 216, "text_extraction_method": "pdftext", "block_counts": [["Span", 84], ["Line", 36], ["Text", 7], ["ListItem", 6], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 217, "text_extraction_method": "pdftext", "block_counts": [["Span", 114], ["Line", 38], ["Text", 10], ["SectionHeader", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4025}}, {"page_id": 218, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 40], ["Text", 7], ["ListItem", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 219, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 37], ["Text", 9], ["ListItem", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 220, "text_extraction_method": "pdftext", "block_counts": [["Span", 106], ["Line", 43], ["Text", 12], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["TextInlineMath", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 221, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 33], ["Text", 8], ["ListItem", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["ListGroup", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 222, "text_extraction_method": "pdftext", "block_counts": [["Span", 82], ["Line", 33], ["Text", 6], ["SectionHeader", 2], ["ListItem", 2], ["Reference", 2], ["Footnote", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 223, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 46], ["TableCell", 33], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 1], ["SectionHeader", 1], ["Table", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6180}}, {"page_id": 224, "text_extraction_method": "pdftext", "block_counts": [["Span", 402], ["Line", 53], ["TableCell", 36], ["ListItem", 6], ["Text", 4], ["TextInlineMath", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Table", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6657}}, {"page_id": 225, "text_extraction_method": "pdftext", "block_counts": [["Span", 237], ["Line", 39], ["Text", 12], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["Code", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 4070}}, {"page_id": 226, "text_extraction_method": "pdftext", "block_counts": [["Span", 144], ["Line", 40], ["ListItem", 9], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListGroup", 2], ["SectionHeader", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 227, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 38], ["Text", 11], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["ListItem", 2], ["SectionHeader", 1], ["Code", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3961}}, {"page_id": 228, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 43], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 229, "text_extraction_method": "pdftext", "block_counts": [["Span", 136], ["Line", 40], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 230, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 21], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 231, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 232, "text_extraction_method": "pdftext", "block_counts": [["Span", 86], ["Line", 29], ["Text", 10], ["SectionHeader", 3], ["Reference", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 233, "text_extraction_method": "pdftext", "block_counts": [["Span", 113], ["Line", 40], ["Text", 10], ["Code", 3], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3868}}, {"page_id": 234, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 39], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 235, "text_extraction_method": "pdftext", "block_counts": [["Span", 103], ["Line", 38], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 236, "text_extraction_method": "pdftext", "block_counts": [["Span", 117], ["Line", 44], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 2], ["Caption", 2], ["Code", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3794}}, {"page_id": 237, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 37], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 238, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 51], ["Text", 5], ["Code", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Figure", 1], ["Caption", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 3419}}, {"page_id": 239, "text_extraction_method": "pdftext", "block_counts": [["Span", 63], ["Line", 17], ["Text", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Code", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data/thinkpython"}