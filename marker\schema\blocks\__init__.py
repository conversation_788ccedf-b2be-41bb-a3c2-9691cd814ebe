from __future__ import annotations

from marker.schema.blocks.base import Block, BlockId, BlockOutput
from marker.schema.blocks.caption import Caption
from marker.schema.blocks.code import Code
from marker.schema.blocks.figure import Figure
from marker.schema.blocks.footnote import Footnote
from marker.schema.blocks.form import Form
from marker.schema.blocks.equation import Equation
from marker.schema.blocks.handwriting import Handwriting
from marker.schema.blocks.inlinemath import InlineMath
from marker.schema.blocks.listitem import ListItem
from marker.schema.blocks.pagefooter import PageFooter
from marker.schema.blocks.pageheader import PageHeader
from marker.schema.blocks.picture import Picture
from marker.schema.blocks.sectionheader import SectionHeader
from marker.schema.blocks.table import Table
from marker.schema.blocks.text import Text
from marker.schema.blocks.toc import TableOfContents
from marker.schema.blocks.complexregion import ComplexRegion
from marker.schema.blocks.tablecell import TableCell
from marker.schema.blocks.reference import Reference
