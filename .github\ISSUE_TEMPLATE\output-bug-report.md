---
name: Output bug report
about: Create a report about poor output quality
title: "[BUG: Output]"
labels: 'bug: output'
assignees: ''

---

## 📝 Describe the Output Issue

A clear and concise description of the incorrect or unexpected output.

## 📄 Input Document

Attach the PDF or input file used.

## 📤 Current Output

Paste the Markdown or HTML that <PERSON><PERSON> generated:

````markdown
Paste output here
`````

## ✅ Expected Output

Describe or paste what you expected Marker to generate.

## ⚙️ Environment

Please fill in all relevant details:

* **Marker version**:
* **Surya version**:
* **Python version**:
* **PyTorch version**:
* **Transformers version**:
* **Operating System**:

## 📟 Command or Code Used

Paste the **exact bash command** or **Python code** you used to run Marker:

<details>
<summary>Click to expand</summary>

```bash
# or Python code block
your_command_here --with-flags
```

</details>

## 📎 Additional Context

Any other relevant info, configs, or assumptions.
